import { FactoryIcon, HomeIcon, NotebookPenIcon } from "lucide-react";
import { PATHS_CONFIG } from "./config";
import { ICreateItemGroupPathManager } from "./types";

export const pathItems: ICreateItemGroupPathManager[] = [
	{
		title: "Dashboard",
		items: [
			{
				id: "home",
				name: "Tela Inicial",
				description: "Bem-vindo ao S.I.M.P - Sistema Integrado de Medição e Manufatura Pormade",
				requiredPermissions: [],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.DASHBOARD,
				icon: HomeIcon,
			},
			{
				id: "production",
				name: "Produção",
				description: "Gerencie e monitore o processo de produção",
				requiredPermissions: [],
				visibleOnMobile: true,
				visibleOnMenu: true,
				route: PATHS_CONFIG.PRODUCTION,
				icon: FactoryIcon,
				subItems: [
					{
						id: "appointments",
						name: "Apontamentos",
						description: "<PERSON><PERSON><PERSON><PERSON> os apontamentos de produção",
						requiredPermissions: [],
						visibleOnMobile: true,
						visibleOnMenu: true,
						route: PATHS_CONFIG.PRODUCTION.subPaths.APPOINTMENTS,
						icon: NotebookPenIcon,
					},
				],
			},
		],
	},
];
