import { ChartAreaInteractive } from "@/modules/dashboard/components/charts/chart-area-interactive";
import { DataTable } from "@/modules/dashboard/components/data-table/data-table";
import { SectionCards } from "@/modules/dashboard/components/section-cards";
import data from "./data.json";

const HomePage = () => {
	return (
		<>
			<SectionCards />
			<div className="px-4 lg:px-6">
				<ChartAreaInteractive />
			</div>
			<DataTable data={data} />
		</>
	);
};

export default HomePage;
