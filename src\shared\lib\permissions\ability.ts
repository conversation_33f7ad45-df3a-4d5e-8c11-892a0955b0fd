import { IUser } from "@/core/auth/types/user.types";
import { AppAbility } from "@/shared/types/permissions/types";
import { AbilityBuilder, createMongoAbility } from "@casl/ability";

export const defineAbilitiesFor = (user: IUser | null): AppAbility => {
	const { can, cannot, build } = new AbilityBuilder<AppAbility>(createMongoAbility);
	const permissions = user?.permissions || [];

	for (const permission of permissions) {
		// const parts = permission.split(":");
		// const [action, subject, scope] = parts;
		// if (scope === "all") {
		// 	can(action, subject);
		// } else if (scope === "own") {
		// 	can(action, subject, { authorId: user?.id });
		// } else {
		// 	can(action, subject, { id: scope });
		// }
	}

	return build();
};
