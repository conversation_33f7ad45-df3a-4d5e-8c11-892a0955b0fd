"use client";

import { getUserInitials } from "@/core/auth/atoms/user.atom";
import { useLogout } from "@/core/auth/hooks/logout/logout.hook";
import { useUser } from "@/core/auth/hooks/user/user.hook";
import { Avatar, AvatarFallback, AvatarImage } from "@/shared/components/shadcn/avatar";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/shared/components/shadcn/dropdown-menu";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/shared/components/shadcn/sidebar";
import { getHighestRoleName } from "@/shared/lib/permissions";
import { useAtomValue } from "jotai";
import { Bell, LogOut, MoreVertical, Settings, User } from "lucide-react";
import { Suspense } from "react";

const UserProfileContent = () => {
	const { user, userName } = useUser();
	const userInitials = useAtomValue(getUserInitials);
	const { logout } = useLogout();

	return (
		<SidebarMenu>
			<SidebarMenuItem>
				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<SidebarMenuButton size="lg" className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
							<Avatar className="h-[40px] w-[40px] rounded-lg">
								<AvatarImage src="" alt={userName ?? "Usuário"} />
								<AvatarFallback className="rounded-lg">{userInitials}</AvatarFallback>
							</Avatar>
							<div className="grid flex-1 text-left text-sm leading-tight">
								<span className="truncate text-text font-semibold">{userName ?? "Usuário"}</span>
								<span className="truncate text-text-light font-light text-xs">{getHighestRoleName(user?.permissions)}</span>
							</div>
							<div className="flex gap-3">
								<Bell className="size-4 w-[24px] h-[24px] text-text-light" />
								<MoreVertical className="size-4 w-[24px] h-[24px] text-text-light" />
							</div>
						</SidebarMenuButton>
					</DropdownMenuTrigger>
					<DropdownMenuContent className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg" align="end" sideOffset={4}>
						<DropdownMenuLabel className="p-0 font-normal">
							<div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
								<Avatar className="h-8 w-8 rounded-lg">
									<AvatarImage src="" alt={userName ?? "Usuário"} />
									<AvatarFallback className="rounded-lg">{userInitials}</AvatarFallback>
								</Avatar>
								<div className="grid flex-1 text-left text-sm leading-tight">
									<span className="truncate font-semibold">{userName ?? "Usuário"}</span>
									<span className="truncate text-xs">{user?.email ? `${user.email}` : "Sem email"}</span>
								</div>
							</div>
						</DropdownMenuLabel>
						<DropdownMenuSeparator />
						<DropdownMenuItem className="gap-2">
							<User size={16} />
							<span>Meu Perfil</span>
						</DropdownMenuItem>
						<DropdownMenuItem className="gap-2">
							<Settings size={16} />
							<span>Configurações</span>
						</DropdownMenuItem>
						<DropdownMenuSeparator />
						<DropdownMenuItem className="gap-2 text-destructive focus:text-destructive" onClick={logout}>
							<LogOut size={16} />
							<span>Sair</span>
						</DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>
			</SidebarMenuItem>
		</SidebarMenu>
	);
};

export const UserProfile = () => {
	return (
		<Suspense fallback={<div>Carregando...</div>}>
			<UserProfileContent />
		</Suspense>
	);
};
