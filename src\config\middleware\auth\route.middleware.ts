import { IRole } from "@/config/enums/role.enum";
import { pathService } from "@/config/path-manager/service";
import { getJWTPayloadProperty } from "@/shared/lib/jwt/get-property";
import { NextRequest, NextResponse } from "next/server";

export const permissionsMiddleware = (request: NextRequest): NextResponse => {
	const { pathname } = request.nextUrl;
	if (pathname.includes(".") && !pathname.startsWith("/api/") && !pathname.startsWith("/auth/")) return NextResponse.next();
	const accessToken = request.cookies.get("access_token")?.value;
	if (!accessToken) return NextResponse.rewrite(new URL("/login", request.url));
	const permissions = getJWTPayloadProperty<IRole[]>(accessToken, "roles") ?? [];
	const currentRoute = pathService.getItemByPath(pathname);
	const hasPermission = currentRoute ? pathService.hasPermission(currentRoute, permissions) : false;
	if (currentRoute && !currentRoute.route.active) return NextResponse.rewrite(new URL("/404", request.url));
	else if (currentRoute && !hasPermission) return NextResponse.rewrite(new URL("/forbidden", request.url));
	return NextResponse.next();
};
