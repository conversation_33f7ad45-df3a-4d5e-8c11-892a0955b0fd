import { ElementType } from "react";
import { IRole } from "../enums/role.enum";

export interface ICreateBaseItemPathManager {
	name: string;
	description: string;
	id: string;
	icon: ElementType;
	route: ICreateRoutePathManager;
}

export interface ICreateRoutePathManager {
	path: string;
	active: boolean;
	subPaths?: Record<string, ICreateRoutePathManager>;
	params?: Record<string, string>;
}

export interface IDefinePermissionPathManager {
	requiredPermissions: IRole[];
}

export interface IVisibilityOnMenuPathManager {
	visibleOnMobile: boolean;
	visibleOnMenu: boolean;
}

export interface ICreateItemPathManager extends ICreateBaseItemPathManager, IDefinePermissionPathManager, IVisibilityOnMenuPathManager {
	subItems?: ICreateItemPathManager[];
}

export interface ICreateItemGroupPathManager {
	title: string;
	items: ICreateItemPathManager[];
}

export interface IPathManagerService {
	getItemById(id: string): ICreateItemPathManager | undefined;
	hasPermission(item: ICreateItemPathManager, userRoles: IRole[]): boolean;
	getMenuForUser(userRoles: IRole[]): ICreateItemGroupPathManager[];
	getItemByPath(path: string, usePartialMatch?: boolean): ICreateItemPathManager | undefined;
}
