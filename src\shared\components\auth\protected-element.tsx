// import { IR<PERSON> } from "@/config/enums/role.enum";
// import { useUser } from "@/core/auth/hooks/user/user.hook";
// import { ReactNode } from "react";

// interface IProtectedElementProps {
// 	requiredPermissions: IRole[];
// 	children: ReactNode;
// 	fallback?: ReactNode;
// 	requireAll?: boolean;
// }

// export function ProtectedElement({ requiredPermissions, children, fallback = null, requireAll = false }: IProtectedElementProps) {
// 	const { permissions } = useUser();

// 	const hasPermission = requireAll
// 		? requiredPermissions.every(role => permissions.includes(role))
// 		: requiredPermissions.some(role => permissions.includes(role));

// 	if (!hasPermission) return <>{fallback}</>;

// 	return <>{children}</>;
// }
