"use server";

import { createGetRequest } from "@/shared/lib/requests";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { AUTH_ENDPOINTS } from "../api/endpoints";
import { AUTH_RETRY_CONFIG, AUTH_TIMEOUTS } from "../constants/auth-timeouts";
import { IUser } from "../types/user.types";

export const getCurrentUser = async (): Promise<ApiResponse<IUser>> => {
	const res = await createGetRequest<IUser>(AUTH_ENDPOINTS.PROFILE, {
		timeout: AUTH_TIMEOUTS.USER_PROFILE,
		retry: AUTH_RETRY_CONFIG.DEFAULT_RETRY,
		retryAttempts: AUTH_RETRY_CONFIG.USER_PROFILE_MAX_RETRIES,
	});
	console.log("getCurrentUser", res);

	if (!res.success) return res;
	const { id, name, roles, email } = res.data;
	return {
		success: true,
		status: 200,
		data: {
			id: id?.toString() || "",
			name: name || "",
			email: email || "",
			roles: roles || [],
			permissions: res.data.permissions || [],
		},
	};
};
