import { axiosInstance } from "@/config/api/instance";
import { AxiosError } from "axios";

interface ILoginRequestParams {
	redirectPath: string;
}

interface IBackendResponse {
	status: number;
	location?: string;
	errorText?: string;
}

export function extractLoginParams(request: URL): ILoginRequestParams {
	return {
		redirectPath: request.searchParams.get("redirect") || "/",
	};
}

export function createErrorResponse(error: string, message: string, status: number): Response {
	return Response.json({ error, message }, { status });
}

export async function fetchBackendLoginUrl(redirectPath: string): Promise<IBackendResponse> {
	try {
		const params = redirectPath !== "/" ? { redirect: redirectPath } : undefined;
		const response = await axiosInstance.get("/auth/login", {
			params,
			maxRedirects: 0,
			validateStatus: status => status < 400,
			withCredentials: true,
		});
		return {
			status: response.status,
			location: response.headers.location,
			errorText: response.status !== 302 ? response.data : undefined,
		};
	} catch (error) {
		const axiosError = error as AxiosError;
		return {
			status: axiosError.response?.status || 500,
			location: axiosError.response?.headers?.location,
			errorText:
				typeof axiosError.response?.data === "string"
					? axiosError.response.data
					: axiosError.response?.data
					? JSON.stringify(axiosError.response.data)
					: axiosError.message,
		};
	}
}

export function isRedirectResponse(response: IBackendResponse): boolean {
	return response.status === 302;
}

export function handleRedirectResponse(response: IBackendResponse): Response {
	if (!response.location) throw new Error("URL de redirecionamento não encontrada na resposta do backend");
	return Response.redirect(response.location, 302);
}

export function handleBackendError(): Response {
	return createErrorResponse("backend_error", "Erro na comunicação com o servidor de autenticação", 500);
}

export function isValidRedirectPath(path: string): boolean {
	if (path.includes("://") || path.startsWith("//") || !path.startsWith("/") || ["<", ">", '"', "'", "&"].some(char => path.includes(char))) {
		return false;
	}
	const allowedPaths = ["/", "/variaveis", "/forbidden"];
	return allowedPaths.some(p => path === p || path.startsWith(p + "/"));
}
