export const PATHS_CONFIG = {
	DASHBOARD: {
		path: "/",
		active: true,
	},
	PRODUCTION: {
		path: "/producao",
		active: false,
		subPaths: {
			APPOINTMENTS: {
				path: "/producao/apontamentos",
				active: true,
				subPaths: {
					APPOINMENT_ID: {
						path: "/producao/apontamentos/:id",
						active: true,
						params: {
							id: "id",
						},
					},
				},
			},
		},
	},
} as const;

export type TPathManagerConfigKey = keyof typeof PATHS_CONFIG;
export type TPathManagerSubPathKey<T extends TPathManagerConfigKey> = (typeof PATHS_CONFIG)[T] extends { subPaths: infer S } ? keyof S : never;
